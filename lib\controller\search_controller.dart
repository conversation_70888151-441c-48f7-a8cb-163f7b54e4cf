import 'package:dio/dio.dart';
import 'package:get/get.dart';

class SearchFoodsController extends GetxController {
  final Dio _dio = Dio();
  // ignore: prefer_final_fields
  RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  set setLoading(bool value) {
    _isLoading.value = value;
  }

  final RxBool _isTriggered = false.obs;
  bool get isTriggered => _isTriggered.value;

  set setTriggered(bool value) {
    _isTriggered.value = value;
  }

  RxList<dynamic> searchResults = <dynamic>[].obs;

  void searchFoods(String key) async {
    setLoading = true;
    try {
      final response = await _dio.get(
        'https://dummyjson.com/products/search?q=$key',
      );
      if (response.statusCode == 200) {
        searchResults.value = response.data['products'];
      } else {
        searchResults.clear();
      }
    } catch (e) {
      print("Search error: $e");
      searchResults.clear();
    } finally {
      setLoading = false;
    }
  }
}
