import 'package:bshop_app/view/card/cart_page.dart';
import 'package:bshop_app/view/card/cart_provider.dart';
import 'package:bshop_app/view/drawer/drawer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Profile Page",
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blueAccent,
        leading: IconButton(
          icon: const Icon(Icons.menu, color: Colors.white),
          onPressed: () => _scaffoldKey.currentState!.openDrawer(),
        ),
        actions: [
          Consumer<CartProvider>(
            builder:
                (context, cart, child) => Padding(
                  padding: const EdgeInsets.all(10),
                  child: Badge(
                    label:
                        (cart.uniqueItemCount > 0)
                            ? Text(cart.uniqueItemCount.toString())
                            : null,
                    backgroundColor:
                        cart.uniqueItemCount > 0
                            ? Colors.red
                            : Colors.transparent,
                    child: IconButton(
                      icon: const Icon(
                        Icons.shopping_cart_outlined,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        Get.to(() => const CartPage());
                      },
                    ),
                  ),
                ),
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: const Center(child: Text("Profile Page")),
    );
  }
}
