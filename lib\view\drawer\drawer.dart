import 'package:bshop_app/view/home/<USER>';
import 'package:bshop_app/view/login/login.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          const _DrawerHeader(),
          _drawerItem(Icons.home, "Home", () => Get.to(() => const HomePage())),
          // _drawerItem(
          //   Icons.install_desktop,
          //   "ຂໍ້ມູນ",
          //   () => Get.to(() => const SettingPage()),
          // ),
          // _drawerItem(
          //   Icons.book_online,
          //   "ຈອງຫ້ອງ",
          //   () => Get.to(() => const SettingPage()),
          // ),
          // _drawerItem(
          //   Icons.login,
          //   "ເຂົ້າພັກ",
          //   () => Get.to(() => const SettingPage()),
          // ),
          // _drawerItem(
          //   Icons.outbox,
          //   "ແຈ້ງອອກ",
          //   () => Get.to(() => const SettingPage()),
          // ),
          // _drawerItem(
          //   Icons.payment,
          //   "ຈ່າຍເງິນ",
          //   () => Get.to(() => const PaymentPage()),
          // ),
          // _drawerItem(
          //   Icons.checklist,
          //   "ລາຍງານ",
          //   () => Get.to(() => const SettingPage()),
          // ),
          // _drawerItem(
          //   Icons.settings,
          //   "Settings",
          //   () => Get.to(() => const SettingPage()),
          // ),
          const Spacer(),
          _drawerItem(Icons.logout, "Logout", () {
            Get.offAll(() => const Loginpage()); // Clears navigation stack
          }),
        ],
      ),
    );
  }

  Widget _drawerItem(IconData icon, String text, VoidCallback onTap) {
    return ListTile(leading: Icon(icon), title: Text(text), onTap: onTap);
  }
}

class _DrawerHeader extends StatelessWidget {
  const _DrawerHeader();

  @override
  Widget build(BuildContext context) {
    return DrawerHeader(
      decoration: const BoxDecoration(color: Color(0xFF025BA3)),
      child: Row(
        children: [
          ClipOval(
            child: Image.asset(
              'assets/images/Logo4.png', // Ensure it's in pubspec.yaml
              width: 100,
              height: 100,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 15), // Fixed spacing
          const Text(
            "Laovieng",
            style: TextStyle(color: Colors.white, fontSize: 24),
          ),
        ],
      ),
    );
  }
}
