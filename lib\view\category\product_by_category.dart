import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';

class ProductByCategory extends StatefulWidget {
  const ProductByCategory({super.key});

  @override
  State<ProductByCategory> createState() => _ProductByCategoryState();
}

final Dio _dio = Dio();
List products = [];
bool _isloading = false;

class _ProductByCategoryState extends State<ProductByCategory> {
  Future<void> getProductsByCategory() async {
    if (_isloading) return;
    setState(() {
      _isloading = true;
    });
    try {
      final response = await _dio.get(
        'https://dummyjson.com/products/category/smartphones',
      );
      setState(() {
        products = response.data['products'];

        _isloading = false;
      });
    } catch (e) {
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}
