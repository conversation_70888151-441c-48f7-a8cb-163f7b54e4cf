import 'package:bshop_app/view/profile/profile_page.dart';
import 'package:bshop_app/view/card/cart_page.dart';
import 'package:bshop_app/controller/tab_index.controller.dart';
import 'package:bshop_app/view/sesrch/search_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:get/get.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  List<Widget> pagesList = [
    MainScreen(),
    SearchPage(),
    CartPage(),
    ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    final tabIndexController = Get.put(TabIndexController());
    return Obx(
      () => Scaffold(
        body: Stack(
          //ເມນູທາງລຸ່ມຫນ້າຈໍ
          children: [
            pagesList[tabIndexController.tabIndex],
            Align(
              alignment: Alignment.bottomCenter,
              child: Theme(
                data: Theme.of(context).copyWith(canvasColor: Colors.blue),
                child: BottomNavigationBar(
                  showSelectedLabels: false,
                  showUnselectedLabels: false,
                  unselectedIconTheme: IconThemeData(color: Colors.white),
                  selectedIconTheme: IconThemeData(color: Colors.black),
                  onTap: (value) {
                    tabIndexController.setTabIndex = value;
                  },
                  currentIndex: tabIndexController.tabIndex,
                  items: [
                    BottomNavigationBarItem(
                      icon:
                          tabIndexController.tabIndex == 0
                              ? Icon(AntDesign.appstore1)
                              : Icon(AntDesign.appstore_o),
                      label: 'Home',
                    ),
                    BottomNavigationBarItem(
                      icon:
                          tabIndexController.tabIndex == 1
                              ? Icon(Icons.search)
                              : Icon(Icons.search),
                      label: 'Search',
                    ),
                    BottomNavigationBarItem(
                      icon: Badge(
                        label: Text('2'),
                        child: Icon(FontAwesome.opencart),
                      ),

                      label: 'Cart',
                    ),
                    BottomNavigationBarItem(
                      icon:
                          tabIndexController.tabIndex == 3
                              ? Icon(FontAwesome.user_circle)
                              : Icon(FontAwesome.user_circle_o),
                      label: 'Profile',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
