import 'package:bshop_app/controller/search_controller.dart';
import 'package:bshop_app/view/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:get/get.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  bool _isloading = false;
  // List products = [];

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SearchFoodsController());
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          toolbarHeight: 74,
          elevation: 0,
          automaticallyImplyLeading: false,
          backgroundColor: Colors.white,
          title: Padding(
            padding: EdgeInsets.only(top: 12),
            child: TextForm<PERSON>ield(
              controller: _searchController,
              keyboardType: TextInputType.text,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                hintText: 'ຄົ້ນຫາສິນຄ້າ',
                hintStyle: TextStyle(color: Colors.grey),
                prefixIcon: Icon(Ionicons.search, color: Colors.grey),
                suffixIcon: GestureDetector(
                  onTap: () {
                    if (controller.isTriggered == false) {
                      controller.searchFoods(_searchController.text);
                      controller.setTriggered = true;
                    } else {
                      controller.searchResults.clear();
                      controller.setTriggered = false;
                      _searchController.clear();
                    }
                  },
                  child: Icon(
                    controller.isTriggered == false
                        ? Ionicons.search_circle
                        : Ionicons.close_circle,
                  ),
                ),
              ),
            ),
          ),
        ),
        body: Obx(() {
          if (controller.isLoading) {
            return Center(child: CircularProgressIndicator());
          }
          return ListView.builder(
            itemCount: controller.searchResults.length,
            itemBuilder: (context, index) {
              final product = controller.searchResults[index];
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: InkWell(
                  onTap: () {
                    showproductDetail(context, product);
                  },
                  child: Card(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.network(
                          product['thumbnail'],
                          fit: BoxFit.cover,
                          height: 100,
                        ),
                        Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Text(
                            product['title'],
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        Text('${product['price']}'),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}
