{"buildFiles": ["C:\\dev\\3_29_0\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterProject\\bshop_app\\android\\app\\.cxx\\Debug\\6m4l1b2l\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterProject\\bshop_app\\android\\app\\.cxx\\Debug\\6m4l1b2l\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}