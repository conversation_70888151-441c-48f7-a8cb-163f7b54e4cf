import 'package:flutter/foundation.dart';
import 'package:get/get.dart'; // ใช้เฉพาะ firstWhereOrNull ได้

class Cartitems {
  final Map product;
  int quantity;

  Cartitems({required this.product, this.quantity = 1});
}

class CartProvider with ChangeNotifier {
  final List<Cartitems> _items = [];
  List<Cartitems> get items => _items;

  void additem(Cartitems item) {
    final existingItem = _items.firstWhereOrNull(
      (cartItem) => cartItem.product['id'] == item.product['id'],
    );

    if (existingItem != null) {
      existingItem.quantity++;
    } else {
      _items.add(Cartitems(product: item.product));
    }
    notifyListeners();
  }

  void removeitem(Cartitems item) {
    final existingItem = _items.firstWhereOrNull(
      (cartItem) => cartItem.product['id'] == item.product['id'],
    );

    if (existingItem != null) {
      if (existingItem.quantity > 1) {
        existingItem.quantity--;
      } else {
        _items.remove(existingItem);
      }
      notifyListeners();
    }
  }

  int get totalitems {
    return _items.fold(0, (sum, item) => sum + item.quantity);
  }

  int get uniqueItemCount {
    // return the number of unique products by ID
    final ids = _items.map((e) => e.product['id']).toSet();
    return ids.length;
  }

  double get totalPrice {
    return _items.fold(0, (sum, item) {
      final price = (item.product['price'] ?? 0).toDouble();
      return sum + (price * item.quantity);
    });
  }
}
