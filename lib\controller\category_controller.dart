import 'package:dio/dio.dart';
import 'package:get/get.dart';

class CategoryController extends GetxController {
  final Dio _dio = Dio();

  RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  set setLoading(bool value) {
    _isLoading.value = value;
  }

  RxList<String> categories = <String>[].obs;
  RxMap<String, List<dynamic>> productsByCategory =
      <String, List<dynamic>>{}.obs;

  @override
  void onInit() {
    super.onInit();
    getCategories();
  }

  // ดึงรายการหมวดหมู่ทั้งหมด
  Future<void> getCategories() async {
    setLoading = true;
    try {
      print("Starting to fetch categories...");
      final response = await _dio.get(
        'https://dummyjson.com/products/categories',
      );
      print("Categories response: ${response.statusCode}");

      if (response.statusCode == 200) {
        categories.value = List<String>.from(response.data);
        print("Categories loaded: ${categories.length}");
        print("Categories: $categories");

        // ดึงสินค้าของแต่ละหมวดหมู่
        await getProductsForAllCategories();
      }
    } catch (e) {
      print("Error fetching categories: $e");
    } finally {
      setLoading = false;
    }
  }

  // ดึงสินค้าของทุกหมวดหมู่
  Future<void> getProductsForAllCategories() async {
    for (String category in categories) {
      await getProductsByCategory(category);
    }
  }

  // ดึงสินค้าตามหมวดหมู่
  Future<void> getProductsByCategory(String category) async {
    try {
      print("Fetching products for category: $category");
      final response = await _dio.get(
        'https://dummyjson.com/products/category/$category?limit=10',
      );
      print("Products response for $category: ${response.statusCode}");

      if (response.statusCode == 200) {
        List<dynamic> products = response.data['products'];
        productsByCategory[category] = products;
        print("Products loaded for $category: ${products.length}");

        // Force update the observable
        productsByCategory.refresh();
      }
    } catch (e) {
      print("Error fetching products for category $category: $e");
    }
  }

  // ดึงสินค้าเพิ่มเติมในหมวดหมู่
  Future<void> loadMoreProducts(String category, int skip) async {
    try {
      final response = await _dio.get(
        'https://dummyjson.com/products/category/$category?limit=10&skip=$skip',
      );
      if (response.statusCode == 200) {
        List<dynamic> newProducts = response.data['products'];
        if (productsByCategory[category] != null) {
          productsByCategory[category]!.addAll(newProducts);
        } else {
          productsByCategory[category] = newProducts;
        }
        productsByCategory.refresh();
      }
    } catch (e) {
      print("Error loading more products for category $category: $e");
    }
  }
}
