import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class TestCategoriesPage extends StatefulWidget {
  const TestCategoriesPage({super.key});

  @override
  State<TestCategoriesPage> createState() => _TestCategoriesPageState();
}

class _TestCategoriesPageState extends State<TestCategoriesPage> {
  final Dio _dio = Dio();
  List<String> categories = [];
  Map<String, List<dynamic>> productsByCategory = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    testAPI();
  }

  Future<void> testAPI() async {
    try {
      print("Testing categories API...");
      
      // Test categories API
      final categoriesResponse = await _dio.get('https://dummyjson.com/products/categories');
      print("Categories response: ${categoriesResponse.statusCode}");
      print("Categories data: ${categoriesResponse.data}");
      
      if (categoriesResponse.statusCode == 200) {
        setState(() {
          categories = List<String>.from(categoriesResponse.data);
        });
        print("Categories loaded: ${categories.length}");
        
        // Test first category products
        if (categories.isNotEmpty) {
          String firstCategory = categories.first;
          print("Testing products for category: $firstCategory");
          
          final productsResponse = await _dio.get(
            'https://dummyjson.com/products/category/$firstCategory?limit=5'
          );
          print("Products response: ${productsResponse.statusCode}");
          print("Products data: ${productsResponse.data}");
          
          if (productsResponse.statusCode == 200) {
            setState(() {
              productsByCategory[firstCategory] = productsResponse.data['products'];
            });
          }
        }
      }
    } catch (e) {
      print("Error: $e");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Categories API'),
        backgroundColor: Colors.blue,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Categories (${categories.length}):',
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  Expanded(
                    flex: 1,
                    child: ListView.builder(
                      itemCount: categories.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: Text(categories[index]),
                          leading: const Icon(Icons.category),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                  if (productsByCategory.isNotEmpty) ...[
                    Text(
                      'Sample Products (${productsByCategory.keys.first}):',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Expanded(
                      flex: 1,
                      child: ListView.builder(
                        itemCount: productsByCategory.values.first.length,
                        itemBuilder: (context, index) {
                          final product = productsByCategory.values.first[index];
                          return ListTile(
                            leading: Image.network(
                              product['thumbnail'],
                              width: 50,
                              height: 50,
                              fit: BoxFit.cover,
                            ),
                            title: Text(product['title']),
                            subtitle: Text('\$${product['price']}'),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
