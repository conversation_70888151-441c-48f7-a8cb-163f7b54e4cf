import 'package:bshop_app/view/home/<USER>';
import 'package:flutter/material.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';

class Loginpage extends StatefulWidget {
  const Loginpage({super.key});

  @override
  State<Loginpage> createState() => _LoginpageState();
}

class _LoginpageState extends State<Loginpage> {
  final TextEditingController _usernamecontroller = TextEditingController();
  final TextEditingController _passwordcontroller = TextEditingController();
  final Dio _dio = Dio();
  bool _isloading = false;
  //Login fuction
  Future<void> login() async {
    try {
      var body = {
        'username': _usernamecontroller.text,
        'password': _passwordcontroller.text,
      };
      setState(() {
        _isloading = true;
      });
      var options = Options(headers: {'Content-type': 'application/json'});
      print('Login: : $body');
      final response = await _dio.post(
        'https://dummyjson.com/auth/login',
        data: body,
        options: options,
      );

      print("Login successful : ${response.data}");

      // Navigate to Home Page after login success
      if (response.statusCode == 200) {
        // Navigator.pushReplacement(
        //   context,
        //   MaterialPageRoute(builder: (context) => const HomePage()),
        // );
        Get.to(HomePage());
      }
      setState(() {
        _isloading = false;
      });
    } catch (e) {
      print('login failed');
      setState(() {
        _isloading = false;
      });
      showsErrorDialog('login failed');
    }
  }

  void showsErrorDialog(String message) {
    AwesomeDialog(
      context: context,
      dialogType: DialogType.error,
      animType: AnimType.rightSlide,
      title: 'Login Failed',
      desc: 'Plase try again...',
      btnOkOnPress: () {},
    ).show();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Container(
            padding: const EdgeInsets.all(30.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black, width: 1),
              borderRadius: BorderRadius.circular(15),
            ),

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 20),
                Center(
                  child: Text(
                    "Welcome to Laovieng Shops",
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
                SizedBox(height: 20),
                Center(
                  child: Image.asset(
                    "assets/images/Logo4.png",
                    width: 200,
                    height: 200,
                  ),
                ),
                SizedBox(height: 15),
                TextField(
                  controller: _usernamecontroller,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    prefixIcon: Icon(Icons.person),
                    label: Text("User name"),
                    hintText: 'ປ້ອນຊື່',
                  ),
                ),
                SizedBox(height: 20),
                // Text("password"),
                TextField(
                  controller: _passwordcontroller,
                  obscureText: true, //ບັງລະຫັດ
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    prefixIcon: Icon(Icons.lock),
                    label: Text("Password"),
                    hintText: 'ໃສ່ລະຫັດ',
                  ),
                ),
                SizedBox(height: 30),
                MaterialButton(
                  onPressed: () {
                    login();
                  },
                  color: Colors.blue,
                  textColor: Colors.white,
                  minWidth: double.infinity,
                  height: 55,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child:
                      _isloading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text("Login"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
