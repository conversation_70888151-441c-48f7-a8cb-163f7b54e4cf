import 'package:bshop_app/controller/category_controller.dart';
import 'package:bshop_app/view/card/cart_page.dart';
import 'package:bshop_app/view/card/cart_provider.dart';
import 'package:bshop_app/view/category/category_products_page.dart';
import 'package:bshop_app/view/drawer/drawer.dart';

import 'package:bshop_app/view/sesrch/search_page.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final List<String> imgList = [
    'https://images.unsplash.com/photo-1520342868574-5fa3804e551c?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=6ff92caffcdd63681a35134a6770ed3b&auto=format&fit=crop&w=1951&q=80',
    'https://images.unsplash.com/photo-1522205408450-add114ad53fe?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=368f45b0888aeb0b7b08e3a1084d3ede&auto=format&fit=crop&w=1950&q=80',
    'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=94a1e718d89ca60a6337a6008341ca50&auto=format&fit=crop&w=1950&q=80',
    'https://images.unsplash.com/photo-1523205771623-e0faa4d2813d?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=89719a0d55dd05e2deae4120227e6efc&auto=format&fit=crop&w=1953&q=80',
    'https://images.unsplash.com/photo-1508704019882-f9cf40e475b4?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=8c6e5e3aba713b17aa1fe71ab4f0ae5b&auto=format&fit=crop&w=1352&q=80',
    'https://images.unsplash.com/photo-1519985176271-adb1088fa94c?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=a0c8d632e977f94e5d312d9893258f59&auto=format&fit=crop&w=1355&q=80',
  ];
  final Dio _dio = Dio();
  List products = [];
  int skip = 0;
  int limit = 10;
  bool _isloading = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    getProducts();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        setState(() {
          getProducts();
        });
      }
    });
  }

  Future<void> getProducts() async {
    if (_isloading) return;
    setState(() {
      _isloading = true;
    });
    try {
      final response = await _dio.get(
        'https://dummyjson.com/products?limit=$limit&skip=$skip&select=title,price,thumbnail,description',
      );
      setState(() {
        if (skip == 0) {
          products = response.data['products'];
        } else {
          products.addAll(response.data['products']);
        }
        skip += limit;
        _isloading = false;
      });
    } catch (e) {
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoryController = Get.put(CategoryController());

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: const Text("ຮ້ານຄ້າ", style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.blueAccent,
        leading: IconButton(
          icon: const Icon(Icons.menu, color: Colors.white),
          onPressed: () => _scaffoldKey.currentState!.openDrawer(),
        ),
        actions: [
          Consumer<CartProvider>(
            builder:
                (context, cart, child) => Padding(
                  padding: const EdgeInsets.all(10),
                  child: Badge(
                    label:
                        (cart.uniqueItemCount > 0)
                            ? Text(cart.uniqueItemCount.toString())
                            : null,
                    backgroundColor:
                        cart.uniqueItemCount > 0
                            ? Colors.red
                            : Colors.transparent,
                    child: IconButton(
                      icon: const Icon(
                        Icons.shopping_cart_outlined,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        Get.to(() => const CartPage());
                      },
                    ),
                  ),
                ),
          ),
        ],
      ),
      drawer: AppDrawer(),
      body: Obx(() {
        if (categoryController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Carousel Banner
              SizedBox(
                height: 200,
                width: double.infinity,
                child: Card(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(),
                  child: CarouselSlider(
                    options: CarouselOptions(
                      autoPlay: true,
                      aspectRatio: 2.0,
                      enlargeCenterPage: true,
                    ),
                    items:
                        imgList
                            .map(
                              (item) => Center(
                                child: Image.network(
                                  item,
                                  fit: BoxFit.cover,
                                  width: 1000,
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Header with Search
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    "ສິນຄ້າຕາມປະເພດ",
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Get.to(() => SearchPage());
                    },
                    child: const Icon(
                      AntDesign.search1,
                      size: 20,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // แสดงสินค้าแยกตามหมวดหมู่
              Expanded(
                child: ListView.builder(
                  itemCount: categoryController.categories.length,
                  itemBuilder: (context, categoryIndex) {
                    String category =
                        categoryController.categories[categoryIndex];
                    List<dynamic> products =
                        categoryController.productsByCategory[category] ?? [];

                    if (products.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // หัวข้อหมวดหมู่
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                category.toUpperCase(),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  // ไปหน้าดูสินค้าทั้งหมดในหมวดหมู่นี้
                                  Get.to(
                                    () => CategoryProductsPage(
                                      category: category,
                                    ),
                                  );
                                },
                                child: const Text("ดูทั้งหมด"),
                              ),
                            ],
                          ),
                        ),
                        // รายการสินค้าในหมวดหมู่ (แสดงแนวนอน)
                        SizedBox(
                          height: 220,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount:
                                products.length > 5
                                    ? 5
                                    : products.length, // แสดงสูงสุด 5 รายการ
                            itemBuilder: (context, productIndex) {
                              final product = products[productIndex];
                              return Container(
                                width: 150,
                                margin: const EdgeInsets.only(right: 10),
                                child: InkWell(
                                  onTap: () {
                                    showproductDetail(context, product);
                                  },
                                  child: Card(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.network(
                                          product['thumbnail'],
                                          fit: BoxFit.cover,
                                          height: 100,
                                          width: double.infinity,
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Column(
                                            children: [
                                              Text(
                                                product['title'],
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 2,
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                ),
                                              ),
                                              const SizedBox(height: 5),
                                              Text(
                                                '\$${product['price']}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.green,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 10),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}

void showproductDetail(BuildContext context, Map product) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            Image.network(product['thumbnail'], fit: BoxFit.cover, height: 120),
            Text(product['title']),
            Text(
              product['price'].toString(),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            Text(
              product['description'],
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      final item = Cartitems(product: product);

                      Provider.of<CartProvider>(context, listen: false).additem(
                        item,
                      ); // ✅ ระบบจะเช็ค id และเพิ่ม quantity ให้เอง

                      // Navigator.pop(context); // ปิด bottom sheet
                    },

                    child: Text('Add to cart'),
                  ),
                ),
                SizedBox(height: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    child: Text('Buy now'),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}
