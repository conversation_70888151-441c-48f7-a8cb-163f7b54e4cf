import 'package:bshop_app/view/home/<USER>';
import 'package:flutter/material.dart';

class ProductList extends StatelessWidget {
  const ProductList({
    super.key,
    required ScrollController scrollController,
    required this.products,
    required bool isloading,
  }) : _scrollController = scrollController,
       _isloading = isloading;

  final ScrollController _scrollController;
  final List products;
  final bool _isloading;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Center(child: const Text("All Product List"))),
      body: Expanded(
        child: GridView.builder(
          controller: _scrollController,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 20,
            mainAxisSpacing: 30,
          ),
          itemCount: products.length,
          itemBuilder: (context, index) {
            if (index == products.length - 1 && _isloading) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Column(
                    children: [CircularProgressIndicator(), Text('Loading...')],
                  ),
                ),
              );
            }
            return InkWell(
              onTap: () {
                showproductDetail(context, products[index]);
              },
              child: Card(
                child: Column(
                  mainAxisSize: MainAxisSize.min, // ✅ ป้องกัน overflow
                  children: [
                    Image.network(
                      products[index]['thumbnail'],
                      fit: BoxFit.cover,
                      height: 100,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Text(
                        products[index]['title'],
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Text('${products[index]['price']}₭'),
                    // Padding(
                    //   padding: const EdgeInsets.symmetric(
                    //     horizontal: 4.0,
                    //     vertical: 4,
                    //   ),
                    //   child: MaterialButton(
                    //     onPressed: () {
                    //       showproductDetail(context, products[index]);
                    //     },
                    //     color: Colors.blue,
                    //     textColor: Colors.white,
                    //     shape: RoundedRectangleBorder(
                    //       borderRadius: BorderRadius.circular(10),
                    //     ),
                    //     minWidth: double.infinity,
                    //     height: 40,
                    //     child: const Text(
                    //       "ເບິ່ງລາຍລະອຽດ",
                    //       style: TextStyle(fontSize: 12),
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
